import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
  RestBindings,
  ResponseObject,
  Response,
} from '@loopback/rest';
import { PermitReport } from '../models';
import { PermitReportRepository, ActionRepository, UserRepository, UserLocationRoleRepository, LocationOneRepository, LocationTwoRepository, LocationThreeRepository, LocationFourRepository, EptwChecklistRepository } from '../repositories';
import { SqsService } from '../services/sqs-service.service';

import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import moment from 'moment';
import { PdfService } from '../services';
import { LocationFilterService } from '../services';
import { close } from 'fs';

@authenticate('jwt')
export class PermitReportController {
  constructor(
    @repository(PermitReportRepository)
    public permitReportRepository: PermitReportRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @repository(UserLocationRoleRepository)
    public userLocationRoleRepository: UserLocationRoleRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(LocationFourRepository)
    public locationFourRepository: LocationFourRepository,
    @repository(EptwChecklistRepository)
    public eptwChecklistRepository: EptwChecklistRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('services.PdfService')
    private pdfService: PdfService,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,
  ) { }

  @post('/permit-reports')
  @response(200, {
    description: 'PermitReport model instance',
    content: { 'application/json': { schema: getModelSchemaRef(PermitReport) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, {
            title: 'NewPermitReport',
            exclude: ['id'],
          }),
        },
      },
    })
    permitReport: Omit<PermitReport, 'id'>,
  ): Promise<PermitReport> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });


    if (permitReport.permitType === 'DC') {


      if (user?.id) {
        permitReport.applicantId = user.id;
      }
      const count = await this.permitReportRepository.count();
      permitReport.maskId = `${moment().format('YYMMDD')}-PTW-DC-${String(count.count + 1).padStart(4, '0')}`;
      permitReport.status = 'In Review with DCSO Representative'
      const pReport = await this.permitReportRepository.create(permitReport);
      let actionItem = {};

      if (permitReport.assessorId) {
        actionItem = {
          application: "PermitToWork",
          actionType: "assessor",

          description: pReport.description,
          dueDate: pReport.permitStartDate,

          status: "open",
          createdDate: pReport.created,
          objectId: pReport.id,
          submittedById: user?.id,
          assignedToId: permitReport.assessorId
        }



        await this.permitReportRepository.updateById(pReport.id, { status: 'Pending HRA Assessment' })
        //send notification
        const permitData = await this.permitReportRepository.findById(pReport.id, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })

        const locationOneName = (permitData as any).locationOne?.name;
        const locationTwoName = (permitData as any).locationTwo?.name;
        const locationThreeName = (permitData as any).locationThree?.name;
        const locationFourName = (permitData as any).locationFour?.name;


        const applicant = await this.userRepository.findById(pReport.applicantId)

        const mailSubject = `Review & Assess ePTW No.: ${permitData.maskId}`;
        const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      <p><strong>ID: </strong> ${permitData.maskId}</p>
          <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>
       
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
          <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;


        if (permitReport.assessorId && user?.id) {
          const assessor = await this.userRepository.findById(permitReport.assessorId);
          if (assessor) { await this.sqsService.sendMessage(assessor, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
        }
      } else {
        actionItem = {
          application: "PermitToWork",
          actionType: "approver",

          description: pReport.description,
          dueDate: pReport.permitStartDate,

          status: "open",
          createdDate: pReport.created,
          objectId: pReport.id,
          submittedById: user?.id,
          assignedToId: permitReport.approverId
        }
        //send notification

        await this.permitReportRepository.updateById(pReport.id, { status: 'Pending Approval' })
        const permitData = await this.permitReportRepository.findById(pReport.id, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })

        const locationOneName = (permitData as any).locationOne?.name;
        const locationTwoName = (permitData as any).locationTwo?.name;
        const locationThreeName = (permitData as any).locationThree?.name;
        const locationFourName = (permitData as any).locationFour?.name;
        const locationFiveName = (permitData as any).locationFive?.name;
        const locationSixName = (permitData as any).locationSix?.name;

        const applicant = await this.userRepository.findById(pReport.applicantId)

        const mailSubject = `Review & Approve ePTW No.: ${permitData.maskId}`;
        const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      <p><strong>ID: </strong> ${permitData.maskId}</p>
          <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>
        
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of Work: </strong> ${permitData.description} </p>
          <p><strong>Location of Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;

        if (permitReport.approverId && user?.id) {
          const dcsoApprover = await this.userRepository.findById(permitReport.approverId);
          if (dcsoApprover) { await this.sqsService.sendMessage(dcsoApprover, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
        }
      }




      await this.actionRepository.create(actionItem)
      return pReport;
    }

    if (permitReport.permitType === 'TC') {
      if (user?.id) {
        permitReport.applicantId = user.id;
      }
      const count = await this.permitReportRepository.count();
      permitReport.maskId = `${moment().format('YYMMDD')}-PTW-TC-${String(count.count + 1).padStart(4, '0')}`;
      permitReport.status = 'In Review with Assessor'
      const pReport = await this.permitReportRepository.create(permitReport);
      const actionItem = {
        application: "PermitToWork",
        actionType: "assessor_one",

        description: pReport.description,
        dueDate: pReport.permitStartDate,

        status: "open",
        createdDate: pReport.created,
        objectId: pReport.id,
        submittedById: user?.id,
        assignedToId: pReport.assessorId
      }
      //send notification
      const permitData = await this.permitReportRepository.findById(pReport.id, {
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },

        ]
      })

      const locationOneName = (permitData as any).locationOne?.name;
      const locationTwoName = (permitData as any).locationTwo?.name;
      const locationThreeName = (permitData as any).locationThree?.name;
      const locationFourName = (permitData as any).locationFour?.name;


      const applicant = await this.userRepository.findById(pReport.applicantId)

      const mailSubject = `${permitData.maskId} - Review Permit`;
      const mailBody = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Permit to Work Email</title>
    </head>
    <body>

    <p><strong>ID: </strong> ${permitData.maskId}</p>
        <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>
     
        <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
        <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitData.description} </p>
        <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
       
        
    </body>
    </html>`;
      if (pReport.assessorId && user?.id) {
        const assessor = await this.userRepository.findById(pReport.assessorId);
        if (assessor) { await this.sqsService.sendMessage(assessor, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }



      await this.actionRepository.create(actionItem)
      return pReport;
    }
    //edit CA
    if (permitReport.permitType === 'CA') {
      if (user?.id) {
        permitReport.applicantId = user.id;
      }
      const count = await this.permitReportRepository.count();
      permitReport.maskId = `${moment().format('YYMMDD')}-PTW-CA-${String(count.count + 1).padStart(4, '0')}`;
      permitReport.status = 'Pending Assessment'
      const pReport = await this.permitReportRepository.create(permitReport);
      const actionItem = {
        application: "PermitToWork",
        actionType: "assessor",

        description: pReport.description,
        dueDate: pReport.permitStartDate,

        status: "open",
        createdDate: pReport.created,
        objectId: pReport.id,
        submittedById: user?.id,
        assignedToId: pReport.assessorId
      }
      //send notification
      if (pReport.assessorId && user?.id) {

        const permitDetails = await this.permitReportRepository.findById(pReport.id, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },
            { relation: 'locationFive' },
            { relation: 'locationSix' },

          ]
        })


        const assessor = await this.userRepository.findById(pReport.assessorId);

        const locationOneName = (permitDetails as any).locationOne?.name;
        const locationTwoName = (permitDetails as any).locationTwo?.name;
        const locationThreeName = (permitDetails as any).locationThree?.name;
        const locationFourName = (permitDetails as any).locationFour?.name;


        const mailSubject = `${permitDetails.maskId} -Assess PTW`;
        const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Permit to Work Email</title>
        </head>
        <body>

        <p><strong>ID: </strong> ${permitDetails.maskId}</p>
            <p><strong>Name of Applicant and Company Name: </strong> ${user.firstName} | ${user.company ? user.company : 'STT GDC'}</p>
            <p><strong>PTW Start Work Date & Time: </strong> ${permitDetails.permitStartDate} </p>
            <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitDetails.description} </p>
            <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
           
            
        </body>
        </html>`;


        if (assessor) { await this.sqsService.sendMessage(assessor, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }



      await this.actionRepository.create(actionItem)
      return pReport;
    }

    throw new HttpErrors.Forbidden('Unauthorized Request')


  }

  @get('/permit-reports/count')
  @response(200, {
    description: 'PermitReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.count(where);
  }

  @authenticate.skip()
  @get('/trigger-action-card/count')
  @response(200, {
    description: 'PermitReport model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async countAction(): Promise<void> {
    // Fetch all actions with status 'open' and actionType 'normalization'
    const openActions = await this.actionRepository.find({
      where: { status: 'open', actionType: 'normalization' },
    });

    // Iterate through each action and update the corresponding PermitReport
    for (const action of openActions) {
      await this.permitReportRepository.updateById(action.objectId, {
        status: 'Pending DCSO Action',
      });
    }
  }


  @get('/permit-reports')
  @response(200, {
    description: 'Array of PermitReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    return this.permitReportRepository.find({
      ...filter,
      where: {
        or: [
          { applicantId: user?.id },
          { assessorId: user?.id },
          { approverId: user?.id },
          { dcsoApproverId: user?.id },
        ],
      },
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
        {
          "relation": "applicant",
          "scope": {
            "fields": ["firstName", "email"]
          }
        }
      ],
    });
  }

  @get('/all-permit-reports')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId

      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };
      const permitReports = await this.permitReportRepository.find({
        ...filter,
        where: whereClause,
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
          {
            "relation": "applicant",
            "scope": {
              "fields": ["firstName", "email"]
            }
          }
        ]
      });
      return permitReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }


  @get('/all-active-permit-reports')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findAllActiveByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId

      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        and: [
          {
            or: filterConditions.map(andConditions => ({
              and: andConditions,
            }))
          },
          {
            or: [
              { status: 'Active' },
              { status: 'Permit Overrun: Pending Closure' }
            ]

          }
        ]
        ,
      };
      const permitReports = await this.permitReportRepository.find({
        ...filter,
        where: whereClause,
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
          {
            "relation": "applicant",
            "scope": {
              "fields": ["firstName", "email"]
            }
          }
        ]
      });
      return permitReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/all-pending-permit-reports')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findAllPendingByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId

      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        and: [
          {
            or: filterConditions.map(andConditions => ({
              and: andConditions,
            }))
          }
        ]
        ,
      };
      const permitReports = await this.permitReportRepository.find({
        ...filter,
        where: whereClause,
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
          {
            "relation": "applicant",
            "scope": {
              "fields": ["firstName", "email"]
            }
          }
        ]
      });
      const filteredReports = permitReports.filter(report => {
        // Extract the status and convert to lowercase for comparison
        const status = report?.status?.toLowerCase();
        // Include reports that have 'pending' but exclude 'permit overrun: pending closure'
        return status?.includes('pending') && status !== 'permit overrun: pending closure';
      });

      return filteredReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/all-archived-permit-reports')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PermitReport, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findAllArchiveByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(PermitReport) filter?: Filter<PermitReport>,
  ): Promise<PermitReport[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId

      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        and: [
          {
            or: filterConditions.map(andConditions => ({
              and: andConditions,
            }))
          }
        ]
        ,
      };
      const permitReports = await this.permitReportRepository.find({
        ...filter,
        where: whereClause,
        include: [
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
          {
            "relation": "applicant",
            "scope": {
              "fields": ["firstName", "email"]
            }
          }
        ]
      });

      const filteredReports = permitReports
        .filter(report => {
          // Only include reports where status does not include 'Active' or 'Pending'
          const status = report?.status?.toLowerCase();
          if (status?.includes('active') || status?.includes('pending')) {
            return false;
          }

          // Parse permitEndDate with the format 'DD-MM-YYYY hh:mm A'
          const permitEndDate = moment(report.permitEndDate, 'DD-MM-YYYY hh:mm A');
          const sevenDaysAgo = moment().subtract(7, 'days');

          // Check if permitEndDate is within the last 7 days
          return permitEndDate.isAfter(sevenDaysAgo);
        });


      return filteredReports;


    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }


  @patch('/permit-reports')
  @response(200, {
    description: 'PermitReport PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
    @param.where(PermitReport) where?: Where<PermitReport>,
  ): Promise<Count> {
    return this.permitReportRepository.updateAll(permitReport, where);
  }

  @get('/permit-reports/{id}')
  @response(200, {
    description: 'PermitReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(PermitReport, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PermitReport, { exclude: 'where' }) filter?: FilterExcludingWhere<PermitReport>
  ): Promise<PermitReport> {
    return this.permitReportRepository.findById(id, filter);
  }

  @patch('/permit-reports/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;



    //update the status
    if (permitData.applicantId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      if (applicant) {

        const mailSubject = `ePTW No: ${permitData.maskId} Approved | ${locationThreeName} > ${locationFourName}`;
        const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

          
<p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitData.description} </p>
          <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;


        await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-dsco-approver/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateDSCOApproverById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [

        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },
      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    //update the status
    if (permitReport.dcsoApproverId) {
      const approver = await this.userRepository.findById(permitReport.dcsoApproverId);
      const applicant = await this.userRepository.findById(permitData.applicantId);
      const actionItem = {
        application: "PermitToWork",
        actionType: "dcso_approver",

        description: permitData.description,
        dueDate: permitData.permitStartDate,

        status: "open",
        createdDate: permitData.created,
        objectId: permitData.id,
        submittedById: user?.id,
        assignedToId: permitReport.dcsoApproverId
      }
      await this.actionRepository.create(actionItem)

      const mailSubject = `Peform Isolation on ePTW No.: ${permitData.maskId} | ${locationThreeName} | ${locationFourName}`;
      const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      
          <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>
         
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
          <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;

      if (approver) {
        this.sqsService.sendMessage(approver, mailSubject, mailBody)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    permitReport.status = 'Pending DCSO Isolation / Acknowledgement';
    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-approver/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateApproverById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });
    //update the status
    if (permitReport.approverId) {
      const approver = await this.userRepository.findById(permitReport.approverId);
      const applicant = await this.userRepository.findById(permitData.applicantId);
      const actionItem = {
        application: "PermitToWork",
        actionType: "approver",

        description: permitData.description,
        dueDate: permitData.permitStartDate,

        status: "open",
        createdDate: permitData.created,
        objectId: permitData.id,
        submittedById: user?.id,
        assignedToId: permitReport.approverId
      }
      await this.actionRepository.create(actionItem)

      const locationOneName = (permitData as any).locationOne?.name;
      const locationTwoName = (permitData as any).locationTwo?.name;
      const locationThreeName = (permitData as any).locationThree?.name;
      const locationFourName = (permitData as any).locationFour?.name;


      const mailSubject = `Review & Approve ePTW No.: ${permitData.maskId}`;
      const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      <p><strong>ID: </strong> ${permitData.maskId}</p>
          <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>
          <p><strong>Name of Assessor: </strong> ${user?.firstName}</p>
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
          <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;

      if (approver) {
        await this.sqsService.sendMessage(approver, mailSubject, mailBody)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }

    if (permitData.permitType === 'CA') {
      permitReport.status = 'Pending Approval'
    } else {
      permitReport.status = 'Pending HRA Approval'
    }

    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-assessor/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateAssessorById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });
    //update the status
    if (permitData.assessorId) {
      const assessor = await this.userRepository.findById(permitData.assessorId);
      const actionItem = {
        application: "PermitToWork",
        actionType: "assessor",

        description: permitData.description,
        dueDate: permitData.permitStartDate,

        status: "open",
        createdDate: permitData.created,
        objectId: permitData.id,
        submittedById: user?.id,
        assignedToId: permitData.assessorId
      }
      await this.actionRepository.create(actionItem)

      const locationOneName = (permitData as any).locationOne?.name;
      const locationTwoName = (permitData as any).locationTwo?.name;
      const locationThreeName = (permitData as any).locationThree?.name;
      const locationFourName = (permitData as any).locationFour?.name;



      if (assessor) {

        const mailSubject = `${permitData.maskId} - Assess PTW:`;
        const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      <p><strong>ID: </strong> ${permitData.maskId}</p>
          <p><strong>Name of Applicant and Company Name: </strong> ${user?.firstName} | ${user?.company ? user.company : 'STT GDC'}</p>
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitData.description} </p>
          <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;
        await this.sqsService.sendMessage(assessor, mailSubject, mailBody)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }


    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-approve/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async updateApproveById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });
    const applicant = await this.userRepository.findById(permitData.applicantId)

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;
    const locationFiveName = (permitData as any).locationFive?.name;
    const locationSixName = (permitData as any).locationSix?.name;
    //update the status
    permitReport.status = "Active";

    const mailSubject = `${permitData.maskId} - PTW Approved`;
    const mailBody = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Permit to Work Email</title>
        </head>
        <body>

           
        <p><strong>ID: </strong> ${permitData.maskId}</p>
            <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
            <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitData.description} </p>
            <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
           
            
        </body>
        </html>`;


    if (applicant) { await this.sqsService.sendMessage(applicant, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }

    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-reject/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async rejectById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    //update the status
    if (permitData.applicantId && permitData.assessorId) {
      const applicant = await this.userRepository.findById(permitData.applicantId);
      const assessor = await this.userRepository.findById(permitData.assessorId);


      const mailSubject = `ePTW No: ${permitData.maskId} Rejected | ${locationThreeName} > ${locationFourName}`;
      const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      <p><strong>ID: </strong> ${permitData.maskId} </p>
          <p><strong>Rejected by (Assessor or Approval or DCSO reps): </strong> ${user?.firstName} </p>
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
          <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;

      if (applicant) {
        await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
    }
    permitReport.status = 'Returned to Applicant'
    await this.actionRepository.updateById(action_id, { status: 'completed' })
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-withdraw/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async withdrawById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const applicant = await this.userRepository.findById(permitData.applicantId);

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    const mailSubject = `ePTW No: ${permitData.maskId} foreclosed / withdrawn | ${locationThreeName} > ${locationFourName}`;
    const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>

                <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
              <p><strong>Foreclosed / withdrawn: </strong> ${user?.firstName}</p>
                
            </body>
            </html>`;

    if (applicant) {
      await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }



    await this.actionRepository.updateById(action_id, { status: 'completed' })
    permitReport.status = 'Withdrawn';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-withdraw/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async withdrawWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });
    const applicant = await this.userRepository.findById(permitData.applicantId);

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    const mailSubject = `ePTW No: ${permitData.maskId} foreclosed / withdrawn | ${locationThreeName} > ${locationFourName}`;
    const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>

                <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
              <p><strong>Foreclosed / withdrawn: </strong> ${user?.firstName}</p>
                
            </body>
            </html>`;

    if (applicant) {
      await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }

    await this.actionRepository.updateAll({ status: "completed" }, { objectId: id });

    permitReport.status = 'Withdrawn';
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-expired/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async expiredWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    console.log(permitData)
    if (permitData.status === 'Permit Overrun: Pending Closure') {
      return;
    }
    const applicant = await this.userRepository.findById(permitData.applicantId);
    const assessor = permitData.assessorId ? await this.userRepository.findById(permitData.assessorId) : '';
    const approver = permitData.approverId ? await this.userRepository.findById(permitData.approverId) : '';

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    const mailSubject = `Permit Overrun: Pending Closure - ePTW No: ${permitData.maskId}. Please close out your ePTW | ${locationThreeName} > ${locationFourName}`;
    const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>



                <p><strong>ePTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work : </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>



            </body>
            </html>`;

    if (applicant) {
      await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
    }

    if (assessor) {
      await this.sqsService.sendMessage(assessor, mailSubject, mailBody)
    }
    if (approver) {
      await this.sqsService.sendMessage(approver, mailSubject, mailBody)
    }
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }


    // await this.actionRepository.updateAll({ status: "completed" }, { objectId: id } );

    permitReport.status = 'Permit Overrun: Pending Closure';
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-suspend/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async suspendPermitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;


    switch (permitData.permitType) {
      case 'DC':
        permitReport.status = 'Revoked: Pending DCSO Action';
        const actionItem = {
          application: "PermitToWork",
          actionType: "normalization",

          description: permitData.description,
          dueDate: permitData.permitStartDate,

          status: "pending_normalization",
          createdDate: permitData.created,
          objectId: permitData.id,
          submittedById: user?.id,
          assignedToId: permitReport.newDcsoApproverId
        }
        await this.actionRepository.create(actionItem)
        if (permitReport.newDcsoApproverId) {
          const applicant = await this.userRepository.findById(permitData.applicantId);
          const dcsoApprover = await this.userRepository.findById(permitReport.newDcsoApproverId);

          const mailSubject = `Normalize Isolation for ePTW No: ${permitData.maskId} | ${locationThreeName} > ${locationFourName}`;
          const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>

        
                <p><strong>Name of Applicant: </strong> ${applicant.firstName} </p>
                <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
              
                
            </body>
            </html>`;

          if (dcsoApprover) {
            await this.sqsService.sendMessage(dcsoApprover, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }
        } else {
          throw new HttpErrors.NotFound('DCSO Approver Not Found')
        }


        break;
      default:
        {
          const applicant = await this.userRepository.findById(permitData.applicantId ?? '');
          const approver = await this.userRepository.findById(permitData.approverId ?? '');
          const assessor = await this.userRepository.findById(permitData.assessorId ?? '');

          const mailSubject = `ePTW No: ${permitData.maskId}. ePTW Revoked | ${locationThreeName} > ${locationFourName}`;
          const mailBody = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Permit to Work Email</title>
          </head>
          <body>

      
              <p><strong>Name of Applicant: </strong> ${applicant.firstName} </p>
              <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
              <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
              <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
            
              
          </body>
          </html>`;

          if (assessor) {
            await this.sqsService.sendMessage(assessor, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          if (approver) {
            await this.sqsService.sendMessage(approver, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          permitReport.status = 'Revoked';

        }
        break;
    }

    await this.permitReportRepository.updateById(id, permitReport);
  }



  @patch('/permit-reports-close-out/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async closeOutWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;


    switch (permitData.permitType) {
      case 'DC':

        if (permitData.status?.toLowerCase().includes('overrun')) {
          permitReport.status = "Permit Overrun: Pending DCSO Action"
        } else {
          permitReport.status = 'Closed: Pending DCSO Action';
        }

        const actionItem = {
          application: "PermitToWork",
          actionType: "normalization",

          description: permitData.description,
          dueDate: permitData.permitStartDate,

          status: "pending_normalization",
          createdDate: permitData.created,
          objectId: permitData.id,
          submittedById: user?.id,
          assignedToId: permitReport.newDcsoApproverId
        }

        await this.actionRepository.create(actionItem)
        if (permitReport.newDcsoApproverId) {
          const applicant = await this.userRepository.findById(permitData.applicantId);
          const dcsoApprover = await this.userRepository.findById(permitReport.newDcsoApproverId);

          const mailSubject = `Normalize Isolation for ePTW No: ${permitData.maskId} | ${locationThreeName} > ${locationFourName}`;
          const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>

        
                <p><strong>Name of Applicant: </strong> ${applicant.firstName} </p>
                <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work required for PTW: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work required for PTW: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
              
                
            </body>
            </html>`;

          if (dcsoApprover) {
            await this.sqsService.sendMessage(dcsoApprover, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }
        } else {
          throw new HttpErrors.NotFound('DCSO Approver Not Found')
        }


        break;
      default:
        {
          const applicant = await this.userRepository.findById(permitData.applicantId ?? '');
          const approver = await this.userRepository.findById(permitData.approverId ?? '');
          const assessor = await this.userRepository.findById(permitData.assessorId ?? '');

          const mailSubject = `Work completed for ePTW No: ${permitData.maskId}. ePTW Closed | ${locationThreeName} > ${locationFourName}`;
          const mailBody = `<!DOCTYPE html>
          <html lang="en">
          <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Permit to Work Email</title>
          </head>
          <body>

      
              <p><strong>Name of Applicant: </strong> ${applicant.firstName} </p>
              <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
              <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
              <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
            
              
          </body>
          </html>`;

          if (assessor) {
            await this.sqsService.sendMessage(assessor, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          if (approver) {
            await this.sqsService.sendMessage(approver, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }

          permitReport.status = 'Closed';

        }
        break;
    }

    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-normalization/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async normalizationWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    // const email = currentUserProfile.email;
    // const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Withdraw `, `Permit ${permitData.maskId} is withdrawn by ${user?.firstName}`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }


    // await this.actionRepository.updateAll({ status: "completed" }, { objectId: id } );
    switch (permitData.permitType) {
      case 'DC':
        if (permitReport.status === 'Return') {
          const currentDateTime = moment();
          const startDateTime = moment(permitData.permitStartDate, 'DD-MM-YYYY hh:mm A');
          const endDateTime = moment(permitData.permitEndDate, 'DD-MM-YYYY hh:mm A');

          if (currentDateTime.isBetween(startDateTime, endDateTime)) {
            permitReport.status = 'Active';
          } else {
            permitReport.status = 'Permit Overrun: Pending Closure';
          }
          const dcsoApprover = await this.userRepository.findById(permitData.dcsoApproverId ?? '');
          const applicant = await this.userRepository.findById(permitData.applicantId ?? '');
          const locationOneName = (permitData as any).locationOne?.name;
          const locationTwoName = (permitData as any).locationTwo?.name;
          const locationThreeName = (permitData as any).locationThree?.name;
          const locationFourName = (permitData as any).locationFour?.name;

          const mailSubject = `Normalize Isolation for ePTW No: ${permitData.maskId} | ${locationThreeName} > ${locationFourName}`;
          const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>
                <p><strong>Name of Applicant: </strong> ${applicant.firstName}  </p>
                <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
             
                
            </body>
            </html>`;

          if (dcsoApprover) {
            await this.sqsService.sendMessage(dcsoApprover, mailSubject, mailBody)
          } else {
            throw new HttpErrors.NotFound(`User not found. Try again`);
          }
        }
        else if (permitReport.status === 'Revoked') {
          permitReport.status = 'Revoked';
        }
        else {

          if (permitData.status?.toLowerCase().includes('overrun')) {
            permitReport.status = "Permit Overrun: Normalized and Closed"
          } else {
            permitReport.status = 'Normalized and Closed';
          }

        }
        await this.actionRepository.updateAll({ status: "completed" }, { objectId: id });
        break;
      default:
        permitReport.status = 'Closed'; break;
    }

    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-timeout/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async timeoutById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    if (permitData.status === 'Application Timed Out') {
      return;
    } else {


      const applicant = await this.userRepository.findById(permitData.applicantId);

      const locationOneName = (permitData as any).locationOne?.name;
      const locationTwoName = (permitData as any).locationTwo?.name;
      const locationThreeName = (permitData as any).locationThree?.name;
      const locationFourName = (permitData as any).locationFour?.name;

      const mailSubject = `Application Timed Out - ePTW No: ${permitData.maskId} Timed Out | ${locationThreeName} > ${locationFourName}`;
      const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>



                <p><strong>ePTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work : </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
               <p><strong>Status of the ePTW : </strong> ${permitData.status} </p>


            </body>
            </html>`;

      if (applicant) {
        await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
      } else {
        throw new HttpErrors.NotFound(`User not found. Try again`);
      }
      //update the status
      // if (permitData.applicantId) {
      //   const applicant = await this.userRepository.findById(permitData.applicantId);
      //   if (applicant) {
      //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Timeout `, `Permit ${permitData.maskId} is  timed out since the permit close time is exceeded`)
      //   } else {
      //     throw new HttpErrors.NotFound(`User not found. Try again`);
      //   }
      // }

      await this.actionRepository.updateById(action_id, { status: 'completed' })
      permitReport.status = 'Application Timed Out';
      await this.permitReportRepository.updateById(id, permitReport);
    }
  }

  @patch('/permit-reports-timeout/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async timeoutWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });
    //update the status
    // if (permitData.applicantId) {
    //   const applicant = await this.userRepository.findById(permitData.applicantId);
    //   if (applicant) {
    //     this.sqsService.sendMessage(applicant, `Permit ${permitData.maskId} has been Timeout `, `Permit ${permitData.maskId}  has timed out since the permit close time is exceeded`)
    //   } else {
    //     throw new HttpErrors.NotFound(`User not found. Try again`);
    //   }
    // }
    const applicant = await this.userRepository.findById(permitData.applicantId);

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    const mailSubject = `Application Timed Out - ePTW No: ${permitData.maskId} Timed Out | ${locationThreeName} > ${locationFourName}`;
    const mailBody = `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Permit to Work Email</title>
            </head>
            <body>



                <p><strong>ePTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
                <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
                <p><strong>Location of High-Risk Work : </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
               <p><strong>Status of the ePTW : </strong> ${permitData.status} </p>


            </body>
            </html>`;

    if (applicant) {
      await this.sqsService.sendMessage(applicant, mailSubject, mailBody)
    } else {
      throw new HttpErrors.NotFound(`User not found. Try again`);
    }
    await this.actionRepository.updateAll({ status: "completed" }, { objectId: id });
    permitReport.status = 'Application Timed Out';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-editing/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async editingById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id);
    //update the status


    await this.actionRepository.updateById(action_id, { status: 'editing' })
    // permitReport.status = 'Editing';
    await this.permitReportRepository.updateById(id, permitReport);
  }

  @patch('/permit-reports-resubmit/{id}/{action_id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async resubmitById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    const applicant = await this.userRepository.findById(permitReport.applicantId)


    const mailSubject = `Perform Isolation on ePTW No: ${permitData.maskId} | ${locationThreeName} > ${locationFourName}`;
    const mailBody = `<!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Permit to Work Email</title>
    </head>
    <body>

    
        <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>

        <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
        <p><strong>Description of High-Risk Work: </strong> ${permitData.description} </p>
        <p><strong>Location of High-Risk Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
       
        
    </body>
    </html>`;
    //update the status
    await this.actionRepository.updateById(action_id, { status: 'completed' })
    let actionItem = {};
    if (permitData.assessorId) {
      actionItem = {
        application: "PermitToWork",
        actionType: "assessor",

        description: permitReport.description,
        dueDate: permitReport.permitStartDate,

        status: "open",
        createdDate: permitReport.created,
        objectId: permitReport.id,
        submittedById: user?.id,
        assignedToId: permitData.assessorId
      }

      await this.permitReportRepository.updateById(id, { status: 'Pending HRA Assessment' })

      if (permitData.assessorId && user?.id) {


        const assessor = await this.userRepository.findById(permitData.assessorId);
        if (assessor) { await this.sqsService.sendMessage(assessor, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
    } else {
      actionItem = {
        application: "PermitToWork",
        actionType: "dcso_approver",

        description: permitReport.description,
        dueDate: permitReport.permitStartDate,

        status: "open",
        createdDate: permitReport.created,
        objectId: permitReport.id,
        submittedById: user?.id,
        assignedToId: permitReport.dcsoApproverId
      }
      await this.permitReportRepository.updateById(id, { status: 'Pending Approval' })
      if (permitReport.dcsoApproverId && user?.id) {
        const dcsoApprover = await this.userRepository.findById(permitReport.dcsoApproverId);
        if (dcsoApprover) { await this.sqsService.sendMessage(dcsoApprover, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
    }






    await this.actionRepository.create(actionItem)
    await this.permitReportRepository.updateById(id, permitReport);
  }


  @patch('/permit-reports-resubmit/{id}')
  @response(204, {
    description: 'PermitReport PATCH success',
  })
  async resubmitWholeById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PermitReport, { partial: true }),
        },
      },
    })
    permitReport: PermitReport,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    await this.permitReportRepository.updateById(id, permitReport);

    const permitData = await this.permitReportRepository.findById(id, {
      include: [
        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },

      ]
    });

    const locationOneName = (permitData as any).locationOne?.name;
    const locationTwoName = (permitData as any).locationTwo?.name;
    const locationThreeName = (permitData as any).locationThree?.name;
    const locationFourName = (permitData as any).locationFour?.name;

    const applicant = await this.userRepository.findById(permitData.applicantId)

    const mailSubject = `Review & Approve ePTW No.: ${permitData.maskId}`;
    const mailBody = `<!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Permit to Work Email</title>
      </head>
      <body>

      <p><strong>ID: </strong> ${permitData.maskId}</p>
          <p><strong>Name of Applicant and Company Name: </strong> ${applicant?.firstName} | ${applicant?.company ? applicant.company : 'STT GDC'}</p>
        
          <p><strong>PTW Start Work Date & Time: </strong> ${permitData.permitStartDate} </p>
          <p><strong>Description of Work: </strong> ${permitData.description} </p>
          <p><strong>Location of Work: </strong> ${locationOneName} &gt; ${locationTwoName} &gt; ${locationThreeName} &gt; ${locationFourName}</p>
         
          
      </body>
      </html>`;




    //update the status

    await this.actionRepository.updateAll({ status: "completed" }, { objectId: id });
    let actionItem = {};
    if (permitReport.assessorId) {
      actionItem = {
        application: "PermitToWork",
        actionType: "assessor",

        description: permitReport.description,
        dueDate: permitReport.permitStartDate,

        status: "open",
        createdDate: moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        objectId: id,
        submittedById: user?.id,
        assignedToId: permitReport.assessorId
      }
      await this.permitReportRepository.updateById(id, { status: 'Pending HRA Assessment' })
      if (permitReport.assessorId && user?.id) {
        const assessor = await this.userRepository.findById(permitReport.assessorId);
        if (assessor) { await this.sqsService.sendMessage(assessor, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
    } else {
      actionItem = {
        application: "PermitToWork",
        actionType: "approver",
        description: permitReport.description,
        dueDate: permitReport.permitStartDate,
        status: "open",
        createdDate: moment().utcOffset("+08:00").format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
        objectId: id,
        submittedById: user?.id,
        assignedToId: permitReport.approverId
      }
      await this.permitReportRepository.updateById(id, { status: 'Pending Approval' })
      if (permitReport.approverId && user?.id) {
        const dcsoApprover = await this.userRepository.findById(permitReport.approverId);
        if (dcsoApprover) { await this.sqsService.sendMessage(dcsoApprover, mailSubject, mailBody) } else { throw new HttpErrors.NotFound(`User not found. Try again`); }
      }
    }




    await this.actionRepository.create(actionItem)

  }

  @put('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() permitReport: PermitReport,
  ): Promise<void> {
    await this.permitReportRepository.replaceById(id, permitReport);
  }

  @del('/permit-reports/{id}')
  @response(204, {
    description: 'PermitReport DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.permitReportRepository.deleteById(id);
  }

  @get('/download-permit-pdf/{id}', {
    responses: {
      '200': {
        description: 'PDF download',
        content: { 'application/pdf': { schema: { type: 'string', format: 'binary' } } },
      },
    },
  })
  @authenticate.skip()
  async downloadPdf(
    @inject(RestBindings.Http.RESPONSE) response: Response,
    @param.path.string('id') id: string,
    @param.filter(PermitReport, { exclude: 'where' }) filter?: FilterExcludingWhere<PermitReport>

  ): Promise<void> {
    try {
      const extendedFilter = {
        include: [
          { relation: 'permitReportAction' },
          { relation: 'applicant' },
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ],
      };
      const permit = await this.permitReportRepository.findById(id, extendedFilter);
      const permitChecklist = await this.eptwChecklistRepository.find();
      const pdfBuffer = await this.pdfService.generatePdf(permit, permitChecklist);
      response.setHeader('Content-Type', 'application/pdf');
      response.setHeader('Content-Disposition', `attachment; filename=${permit.maskId}.pdf`);
      response.end(pdfBuffer);
    } catch (error) {
      console.error('Error generating PDF:', error);
      response.status(500).send('Error generating PDF');
    }
  }

  @authenticate.skip()
  @post('/permits/statistics')
  @response(200, {
    description: 'Dashboard permit statistics including first-time approval rate and revoked permits.',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getPermitStatistics(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};
    const dateFormat = 'DD-MM-YYYY hh:mm A'; // Custom date format

    console.log(`Received filterParams: from=${from}, to=${to}`);

    const permits = await this.permitReportRepository.find({
      include: [
        { relation: 'actions' },
        { relation: 'applicant' },

        { relation: 'locationOne' },
        { relation: 'locationTwo' },
        { relation: 'locationThree' },
        { relation: 'locationFour' },
        { relation: 'locationFive' },
        { relation: 'locationSix' },


      ],
    });

    // Filter permits based on the date range
    const filteredPermits = permits.filter((permit) => {
      const createdDate = moment(permit.created, dateFormat); // Parse using custom format
      if (!createdDate.isValid()) {
        console.error(`Invalid date format for permit: ${permit.created}`);
        return false;
      }

      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'minute', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'minute');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'minute');
      }
      return true;
    });

    const firstTimeApprovedPermits: any[] = [];
    const revokedPermits: any[] = [];

    filteredPermits.forEach((permit) => {
      const isRevoked = permit.status?.toLowerCase() === 'revoked';
      if (isRevoked) {
        revokedPermits.push(permit);
        return;
      }

      const hasActions = permit.actions && permit.actions.length > 0;

      if (hasActions) {
        const isFirstTimeApproved = permit.actions.every(
          (action) =>
            action.status &&
            !action.status.toLowerCase().includes('returned') &&
            action.status.toLowerCase().includes('closed')
        );

        if (isFirstTimeApproved && permit?.status?.toLowerCase() === 'approved') {
          firstTimeApprovedPermits.push(permit);
        }
      } else {
        if (permit?.status?.toLowerCase() === 'approved') {
          firstTimeApprovedPermits.push(permit);
        }
      }
    });

    const firstTimeApprovalRate =
      filteredPermits.length > 0
        ? (firstTimeApprovedPermits.length / filteredPermits.length) * 100
        : 0;

    return {
      totalPermits: filteredPermits.length,
      firstTimeApprovedCount: firstTimeApprovedPermits.length,
      firstTimeApprovalRate: `${firstTimeApprovalRate.toFixed(2)}%`,
      revokedCount: revokedPermits.length,
      detailedResults: {
        firstTimeApprovedPermits,
        revokedPermits,
        allPermits: filteredPermits, // Include all permits in case you need them
      },
    };
  }


  @authenticate.skip()
  @post('/permits/type-distribution')
  @response(200, {
    description: 'Pie chart data for type of permits (count and percentage)',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getPermitTypeDistribution(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              from: { type: 'string', format: 'date-time' },
              to: { type: 'string', format: 'date-time' },
            },
            required: [],
          },
        },
      },
    })
    filterParams: { from?: string; to?: string }
  ): Promise<object> {
    const { from, to } = filterParams ?? {};
    const dateFormat = 'DD-MM-YYYY hh:mm A'; // Custom date format for permit dates

    console.log(`Received filterParams: from=${from}, to=${to}`);

    // Fetch all permits
    const permits = await this.permitReportRepository.find();

    // Filter permits based on the date range
    const filteredPermits = permits.filter((permit) => {
      const createdDate = moment(permit.created, dateFormat); // Parse using custom format
      if (!createdDate.isValid()) {
        console.error(`Invalid date format for permit: ${permit.created}`);
        return false; // Skip invalid dates
      }

      if (from && to) {
        return createdDate.isBetween(moment(from), moment(to), 'minute', '[]');
      } else if (from) {
        return createdDate.isSameOrAfter(moment(from), 'minute');
      } else if (to) {
        return createdDate.isSameOrBefore(moment(to), 'minute');
      }
      return true;
    });

    const totalPermits = filteredPermits.length;

    const permitTypeCounts: Record<string, number> = filteredPermits.reduce((acc: any, permit: any) => {
      const type = permit.permitType ?? 'Unknown'; // Handle missing permitType
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    // Calculate percentages and format response
    const permitTypeDistribution = Object.keys(permitTypeCounts).map((type) => {
      const count = permitTypeCounts[type];
      const percentage = totalPermits > 0 ? ((count / totalPermits) * 100).toFixed(2) : '0.00';
      return {
        type,
        count,
        percentage: `${percentage}%`,
      };
    });

    return {
      totalPermits,
      permitTypeDistribution,
    };
  }

  @authenticate.skip()
  @get('/permits/type-distribution-by-month')
  @response(200, {
    description: 'Stacked bar chart data for permits by type and month-year',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getPermitTypeByMonthYear(): Promise<object> {
    const permits = await this.permitReportRepository.find();

    const permitCountsByMonth: Record<string, Record<string, number>> = {};

    permits.forEach((permit) => {
      const permitType = permit.permitType || 'Unknown'; // Handle missing permit type
      const createdDate = moment(permit.created, 'DD-MM-YYYY hh:mm A');
      const monthYear = createdDate.format('MMMM-YYYY'); // Format as "Month-Year" (e.g., "January-2025")

      // Initialize month-year entry if not present
      if (!permitCountsByMonth[monthYear]) {
        permitCountsByMonth[monthYear] = {};
      }

      // Initialize permit type entry if not present for this month-year
      permitCountsByMonth[monthYear][permitType] = (permitCountsByMonth[monthYear][permitType] || 0) + 1;
    });

    // Format data for stacked bar chart
    const categories = Object.keys(permitCountsByMonth); // X-axis (Month-Year)
    const seriesData: Record<string, { category: string; count: number }[]> = {}; // Y-axis (Permit types)

    categories.forEach((monthYear) => {
      Object.entries(permitCountsByMonth[monthYear]).forEach(([type, count]) => {
        if (!seriesData[type]) {
          seriesData[type] = [];
        }
        seriesData[type].push({
          category: monthYear,
          count: count,
        });
      });
    });

    return {
      categories,
      series: Object.keys(seriesData).map((type) => ({
        type,
        data: seriesData[type],
      })),
    };
  }

  @authenticate.skip()
  @get('/permits/risk-profile')
  @response(200, {
    description: 'Pie chart data for risk profile of ePermit-to-work activities',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getRiskProfile(): Promise<object> {
    const permits = await this.permitReportRepository.find();

    const highRiskActivityCounts: Record<string, number> = {};

    // Step 1: Count occurrences of all high-risk activities
    permits.forEach((permit) => {
      const highRiskSelected = permit.high_risk?.selectedPermits || [];
      highRiskSelected.forEach((activity: any) => {
        if (activity.checked === 'Yes') {
          const label = activity.label || 'Unknown';
          highRiskActivityCounts[label] = (highRiskActivityCounts[label] || 0) + 1;
        }
      });
    });

    // Step 2: Sort activities by count and get top 3
    const sortedActivities = Object.entries(highRiskActivityCounts)
      .sort(([, countA], [, countB]) => countB - countA)
      .map(([label]) => label);

    const top3Activities = sortedActivities.slice(0, 3);
    const remainingActivities = sortedActivities.slice(3);

    // Step 3: Group high-risk activities by month and year
    const highRiskBreakdownByMonth: Record<string, Record<string, number>> = {};

    permits.forEach((permit) => {
      const createdDate = moment(permit.created, 'DD-MM-YYYY hh:mm A');
      const monthYear = createdDate.format('YYYY-MM'); // Group by Year-Month

      if (!highRiskBreakdownByMonth[monthYear]) {
        highRiskBreakdownByMonth[monthYear] = {
          [top3Activities[0] || 'Activity 1']: 0,
          [top3Activities[1] || 'Activity 2']: 0,
          [top3Activities[2] || 'Activity 3']: 0,
          Others: 0,
        };
      }

      const highRiskSelected = permit.high_risk?.selectedPermits || [];
      highRiskSelected.forEach((activity: any) => {
        if (activity.checked === 'Yes') {
          const label = activity.label || 'Unknown';
          if (top3Activities.includes(label)) {
            highRiskBreakdownByMonth[monthYear][label] += 1;
          } else {
            highRiskBreakdownByMonth[monthYear]["Others"] += 1;
          }
        }
      });
    });

    // Step 4: Format response for each month-year
    const formattedResponse = Object.keys(highRiskBreakdownByMonth).map((monthYear) => {
      const data = highRiskBreakdownByMonth[monthYear];
      const totalActivities = Object.values(data).reduce((sum, count) => sum + count, 0);

      return {
        monthYear,
        breakdown: Object.entries(data).map(([label, count]) => ({
          label,
          count,
          percentage: ((count / totalActivities) * 100).toFixed(2) + '%',
        })),
        totalActivities,
      };
    });

    return {
      title: "Dynamic High-Risk Activity Breakdown",
      top3Activities,
      highRiskBreakdownByMonth: formattedResponse,
    };
  }

  @authenticate.skip()
  @get('/permits/high-risk-breakdown')
  @response(200, {
    description: 'Histogram data for breakdown of high-risk activities',
    content: { 'application/json': { schema: { type: 'object' } } },
  })
  async getHighRiskBreakdown(): Promise<object> {
    const permits = await this.permitReportRepository.find();

    const highRiskBreakdown: Record<string, any> = {};

    permits.forEach((permit) => {
      const createdDate = moment(permit.created, 'DD-MM-YYYY hh:mm A');
      const monthYear = createdDate.format('YYYY-MM'); // Grouping by Year-Month

      if (!highRiskBreakdown[monthYear]) {
        highRiskBreakdown[monthYear] = {};
      }

      const highRiskSelected = permit.high_risk?.selectedPermits || [];

      highRiskSelected.forEach((activity: any) => {
        if (activity.checked === 'Yes') {
          const activityLabel = activity.label || 'Unknown Activity';
          if (!highRiskBreakdown[monthYear][activityLabel]) {
            highRiskBreakdown[monthYear][activityLabel] = 0;
          }
          highRiskBreakdown[monthYear][activityLabel] += 1;
        }
      });
    });

    // Format response
    const formattedBreakdown = Object.keys(highRiskBreakdown).map((monthYear) => {
      return {
        monthYear,
        activities: Object.entries(highRiskBreakdown[monthYear]).map(([activity, count]) => ({
          activity,
          count,
        })),
      };
    });

    return {
      title: "High-Risk Activity Breakdown",
      highRiskBreakdownByMonth: formattedBreakdown,
    };
  }

}


