import { BindingScope, injectable } from '@loopback/core';
import PdfPrinter from 'pdfmake';
import path from 'path';
import fs from 'fs';
import moment from 'moment';
import axios from 'axios';
import sharp from 'sharp';
import { repository } from '@loopback/repository';
import { EptwChecklistRepository } from '../repositories/eptw-checklist.repository';

interface Option {
  checked: number;
  label: string;
}

interface Location {
  id: string;
  name: string;
  locationFiveId?: string;
  locationFourId?: string;
  title?: string;
}

interface ZonesAndLevels {
  locationFive: Location;
  locationSix: Location;
}

interface ReportData {
  zonesAndLevels?: ZonesAndLevels[];
  locationFive?: Location;
  locationSix?: Location;
}

interface DCSOItem {
  status: string;
  name: string;
  dcsoNormComments?: string;
  dcsoReturnedDate?: string;
  dcsoNormSign?: string;
  dcsoNormSignedDate?: string;
}

@injectable({ scope: BindingScope.TRANSIENT })
export class PdfService {
  constructor(
    @repository(EptwChecklistRepository)
    public eptwChecklistRepository: EptwChecklistRepository,
  ) { }

  async generatePdf(reportData: any, permitChecklist: any): Promise<Buffer> {
    const dcop = reportData?.dcop || {};
    const highRisk = reportData?.high_risk || {};
    const uploads = reportData?.uploads || [];
    const fireSystems = dcop?.fireSystems || [];
    const securitySystems = dcop?.securitySystems || [];
    const fireSysMore = dcop?.fireSysMore || []

    const energySystems = dcop?.energySystems || [];

    const permits = reportData?.high_risk?.selectedPermits || reportData?.selectedPermits || [];
    const checklists = reportData?.high_risk?.checklists || [];

    const getConfirmationLabel = (data: any, checklistId: any): string => {
      if (!checklists[checklistId]) {
        return 'N/A';
      }
      const option = checklists[checklistId][data.id]?.options?.find((option: Option) => option.checked === 1);
      return option ? option.label : 'N/A';
    };

    const getRemarks = (data: any, checklistId: any) => {
      return checklists[checklistId][data.id]?.remarks || 'N/A';
    };

    const getPersonnel = (data: any, checklistId: any) => {
      return checklists[checklistId][data.id]?.personnel || 'N/A';
    };

    const getStatus = (status: any) => {
      if (status === 'Active') {
        const permitStartDate = moment(reportData.permitStartDate, 'DD-MM-YYYY hh:mm A');
        const currentTime = moment();

        if (permitStartDate.isSameOrAfter(currentTime)) {
          return 'Active';
        } else {
          return 'Pending Work Commencement';
        }
      }
      return status;
    };

    async function convertToDataUrl(imagePath: string): Promise<string> {
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        // Fetch the image from the URL
        try {
          const response = await axios.get(imagePath, { responseType: 'arraybuffer' });
          const buffer = Buffer.from(response.data, 'binary');
          // Check if the image is SVG
          if (imagePath.endsWith('.svg')) {
            const pngBuffer = await sharp(buffer).png().toBuffer();
            return `data:image/png;base64,${pngBuffer.toString('base64')}`;
          } else {
            return `data:image/png;base64,${buffer.toString('base64')}`;
          }
        } catch (err: any) {
          throw new Error(`Error fetching image from URL: ${err.message}`);
        }
      } else {
        // Read image from local file system
        const absolutePath = path.resolve(__dirname, imagePath);
        return new Promise((resolve, reject) => {
          fs.readFile(absolutePath, (err, data) => {
            if (err) {
              reject(err);
            } else {
              // Check if the image is SVG
              if (imagePath.endsWith('.svg')) {
                sharp(data)
                  .png()
                  .toBuffer()
                  .then((pngBuffer) => {
                    resolve(`data:image/png;base64,${pngBuffer.toString('base64')}`);
                  })
                  .catch((err) => {
                    reject(err);
                  });
              } else {
                resolve(`data:image/png;base64,${data.toString('base64')}`);
              }
            }
          });
        });
      }
    }

    const fonts = {
      Roboto: {
        normal: 'fonts/Roboto-Regular.ttf',
        bold: 'fonts/Roboto-Medium.ttf',
        italics: 'fonts/Roboto-Italic.ttf',
        bolditalics: 'fonts/Roboto-MediumItalic.ttf'
      }
    };

    const dcsoActions = reportData?.closure?.dcso
      ? await Promise.all(
        reportData.closure.dcso.map(async (item: DCSOItem, i: number) => {
          return {
            stack: [
              { text: `Normalization Action ${i + 1}`, style: 'header' },
              item.status === 'Returned'
                ? {
                  stack: [
                    { text: item.name, style: 'info' },
                    { text: `Comments: ${item.dcsoNormComments}`, style: 'info' },
                    { text: `Returned on: ${item.dcsoReturnedDate}`, style: 'info' },
                  ],
                }
                : {
                  stack: [
                    {
                      text: "I confirm that where applicable, isolation has been normalized in compliance with current legislations and STT GDC's requirements. To the best of my knowledge, the work area and the equipment have been placed in a safe condition to continue operation.",
                      style: "info"
                    },
                    ...(item.dcsoNormSign ? [{
                      image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${item.dcsoNormSign}`),
                      width: 200
                    }] : []),
                    { text: item.name, style: 'info' },
                    ...(item.dcsoNormSignedDate ? [{ text: `Signed on: ${item.dcsoNormSignedDate}`, style: 'info' }] : []),
                    ...(item.dcsoNormComments ? [{ text: `Comments: ${item.dcsoNormComments}`, style: 'info' }] : []),
                  ].filter(Boolean),
                },
            ],
            margin: [0, 10, 0, 10],
          };
        })
      )
      : [];

    const closureSection = reportData.closure ? {
      stack: [
        { text: 'Revoked', style: 'header', margin: [0, 10, 0, 5] },
        { text: reportData.closure.suspendComments || '', style: 'paragraph', margin: [0, 0, 0, 10] },
        {
          columns: [
            { text: reportData.closure.suspendedBy || '', style: 'info' },
            { text: `Signed on: ${reportData.closure.suspendedDate || ''}`, style: 'info' }
          ],
          margin: [0, 0, 0, 10]
        }
      ]
    } : null;

    // Ensure fonts exist
    const fontStyles: (keyof typeof fonts.Roboto)[] = ['normal', 'bold', 'italics', 'bolditalics'];
    for (const style of fontStyles) {
      if (!fs.existsSync(fonts.Roboto[style])) {
        throw new Error(`Font file not found: ${fonts.Roboto[style]}`);
      }
    }

    const printer = new PdfPrinter(fonts);

    const docDefinition = {
      pageMargins: [40, 60, 40, 40] as [number, number, number, number],
      header: {
        columns: [
          {
            stack: [
              {
                image: await convertToDataUrl("https://media.datacenterdynamics.com/media/images/ST_Telemedia_Global_Data_Centres_L.2e16d0ba.fill-279x140.jpg"),
                width: 100,
                alignment: 'center',
              },
            ],
            width: '*',
            alignment: 'center',
          },
          {
            stack: [
              {
                text: 'Permit to Work',
                alignment: 'center',
                fontSize: 13,
                bold: true,
                margin: [0, 0, 0, 0]
              },
              {
                text: reportData.maskId,
                alignment: 'center',
                margin: [0, 0, 0, 0],
                fontSize: 10
              }
            ],
            alignment: 'center',
            width: '*',
          },
          {
            text: getStatus(reportData.status),
            alignment: 'center',
            color: 'blue',
            fontSize: 10,
            width: '*',
          }
        ],
        margin: [10, 10, 10, 10]
      },
      content: [
        {
          columns: [
            {
              stack: [
                { text: 'Applied On', margin: [0, 0, 0, 5] },
                { text: moment(reportData.created, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A') || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            },
            {
              stack: [
                { text: 'Work Start Date & Time', margin: [0, 0, 0, 5] },
                { text: moment(reportData.permitStartDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A') || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            }
          ]
        },
        {
          columns: [
            {
              stack: [
                { text: 'Work End Date & Time', margin: [0, 0, 0, 5] },
                { text: moment(reportData.permitEndDate, 'DD-MM-YYYY hh:mm A').format('Do MMM YYYY hh:mm A') || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            },
            {
              stack: [
                { text: 'Applied By', margin: [0, 0, 0, 5] },
                { text: reportData.applicant?.firstName || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            }
          ]
        },
        {
          canvas: [
            { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
          ],
          margin: [0, 10, 0, 10]
        },
        {
          columns: [
            {
              stack: [
                { text: 'Location', margin: [0, 0, 0, 5] },
                { text: (reportData.locationOne && reportData.locationTwo) ? reportData.locationTwo.name + ', ' + reportData.locationOne.name : '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            },
            {
              stack: [
                { text: 'Business Unit', margin: [0, 0, 0, 5] },
                { text: reportData.locationThree && reportData.locationThree.name || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            }
          ]
        },
        {
          columns: [
            {
              stack: [
                { text: 'Project/DC name', margin: [0, 0, 0, 5] },
                { text: reportData.locationFour && reportData.locationFour.name || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            }
          ]
        },
        {
          // Zones and Levels
          stack: reportData.zonesAndLevels && reportData.zonesAndLevels.length > 0
            ? reportData.zonesAndLevels.map((item: ZonesAndLevels) => ({
              columns: [
                {
                  stack: [
                    { text: 'Level', margin: [0, 0, 0, 5] },
                    { text: item.locationFive?.name || '', bold: true, margin: [0, 5, 0, 10] }
                  ],
                  margin: [0, 0, 10, 10]
                },
                {
                  stack: [
                    { text: 'Zone', margin: [0, 0, 0, 5] },
                    { text: item.locationSix?.name || '', bold: true, margin: [0, 5, 0, 10] }
                  ],
                  margin: [10, 0, 0, 10]
                }
              ],
              margin: [0, 0, 0, 10]
            }))
            : [
              {
                columns: [
                  {
                    stack: [
                      { text: 'Level', margin: [0, 0, 0, 5] },
                      { text: reportData.locationFive?.name || '', bold: true, margin: [0, 5, 0, 10] }
                    ],
                    margin: [0, 0, 10, 10]
                  },
                  {
                    stack: [
                      { text: 'Zone', margin: [0, 0, 0, 5] },
                      { text: reportData.locationSix?.name || '', bold: true, margin: [0, 5, 0, 10] }
                    ],
                    margin: [10, 0, 0, 10]
                  }
                ],
                margin: [0, 0, 0, 10]
              }
            ]
        },
        {
          columns: [
            {
              stack: [
                { text: 'Permit Type', margin: [0, 0, 0, 5] },
                { text: reportData.permitType && reportData.permitType === 'CA' ? 'Construction' : reportData.permitType, bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            }
          ]
        },
        {
          canvas: [
            { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
          ],
          margin: [0, 10, 0, 10]
        },
        {
          columns: [
            {
              stack: [
                { text: 'Description', margin: [0, 0, 0, 5] },
                { text: reportData.description || '', bold: true, margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            },
            {
              stack: [
                { text: '', margin: [0, 0, 0, 5] },
                { text: '', margin: [0, 5, 0, 10] }
              ],
              margin: [0, 0, 0, 10]
            }
          ]
        },

        ...(reportData.permitType !== 'CA' ? [
          { text: 'Uploaded Documents', margin: [0, 10, 0, 10] },
          {
            stack: uploads && uploads.length > 0 ? uploads.map((item: string) => ({
              text: item.split('/').pop(),
              link: `${process.env.IMAGE_URL}/docs/${item}`,
              color: 'blue',
              decoration: 'underline',
              margin: [0, 0, 0, 10]
            })) : [{ text: 'No documents uploaded' }],
          },
          {
            canvas: [
              { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
            ],
            margin: [0, 10, 0, 10]
          }
        ] : []),

        ...(reportData.status === 'Revoked' ? [
          {
            columns: [
              {
                stack: [
                  { text: 'Revoke Comments', margin: [0, 0, 0, 5] },
                  { text: dcop?.suspendComments || 'No comments', bold: true, margin: [0, 5, 0, 10] }
                ],
                margin: [0, 0, 0, 10]
              },
              {
                stack: [
                  { text: 'Applied by', margin: [0, 0, 0, 5] },
                  { text: dcop?.applicantName || 'Not specified', bold: true, margin: [0, 5, 0, 10] },
                  { text: dcop?.company || 'Not specified', margin: [0, 5, 0, 10] }
                ],
                margin: [0, 0, 0, 10]
              }
            ]
          }
        ] : []),

        ...(reportData.permitType !== 'CA' ? [
          {
            columns: [
              {
                stack: [
                  { text: 'Reference MOP Title', margin: [0, 0, 0, 5] },
                  { text: dcop?.mopTitle || 'Not specified', bold: true, margin: [0, 5, 0, 10] }
                ],
                margin: [0, 0, 0, 10]
              },
              {
                stack: [
                  { text: 'Change Ticket No', margin: [0, 0, 0, 5] },
                  { text: dcop?.ticketNo || 'Not specified', bold: true, margin: [0, 5, 0, 10] }
                ],
                margin: [0, 0, 0, 10]
              }
            ]
          },
          {
            columns: [
              {
                stack: [
                  { text: 'Applicant Contact Number', margin: [0, 0, 0, 5] },
                  { text: dcop?.contactNo || 'Not specified', bold: true, margin: [0, 5, 0, 10] }
                ],
                margin: [0, 0, 0, 10]
              },
              {
                stack: [
                  { text: 'Company Contact Number', margin: [0, 0, 0, 5] },
                  { text: dcop?.companyContactNo || 'Not specified', bold: true, margin: [0, 5, 0, 10] }
                ],
                margin: [0, 0, 0, 10]
              }
            ],
            canvas: [
              { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
            ],
            margin: [0, 10, 0, 10]
          }
        ] : []),

        // Fire Isolation
        ...(dcop?.isDcFire === 'Yes'
          ? [
            {
              stack: [
                { text: 'Fire Isolation Location(s)', style: 'header' },
                ...fireSystems.map((item: any) => ({
                  table: {
                    widths: ['*'],
                    body: [
                      [
                        {
                          stack: [
                            {
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      text: `${item.loc5.loc5Title} - ${item.loc5.name}`,
                                      style: 'subheader',
                                      fillColor: '#005284',
                                      color: '#FFFFFF',
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders'
                            },
                            {
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      text: `${item.loc6?.loc6Title || ''} - ${item.loc6?.name || ''}`,
                                      style: 'subheader',
                                      fillColor: '#E1EBEE',
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders'
                            },
                            ...item.systems.map((item1: any) => ({
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      columns: [
                                        {
                                          text: `Is the ${item1.label} Isolated?`,
                                          style: 'question',
                                          width: '*',
                                        },
                                        {
                                          text: item1.isolated === 1 ? 'Yes' : item1.isolated === 0 ? 'No' : 'Not Applicable',
                                          style: item1.isolated === 1 ? 'green' : item1.isolated === 0 ? 'red' : 'orange',
                                          width: '*',
                                        },
                                        ...(item1.remarks ? [{ text: `Remarks: ${item1.remarks}`, width: '*' }] : [])
                                      ].filter(Boolean),
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders',
                              margin: [0, 0, 0, 10],
                              keepTogether: true
                            }))
                          ],
                          margin: [10, 10, 10, 10],
                          keepTogether: true
                        }
                      ]
                    ],
                    keepTogether: true
                  },
                  layout: {
                    hLineWidth: function (i: number, node: any) {
                      return (i === 0 || i === node.table.body.length) ? 1 : 0;
                    },
                    vLineWidth: function (i: number, node: any) {
                      return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                    },
                    hLineColor: function (i: number, node: any) {
                      return (i === 0 || i === node.table.body.length) ? '#E0E0E0' : 'white';
                    },
                    vLineColor: function (i: number, node: any) {
                      return (i === 0 || i === node.table.widths.length) ? '#E0E0E0' : 'white';
                    },
                  },
                  margin: [0, 10, 0, 10]
                }))
              ],
              canvas: [
                { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
              ],
              margin: [0, 10, 0, 10]
            },
            {
              stack: [
                { text: 'Fire System Further Details', style: 'header', margin: [0, 10, 0, 5] },
                ...fireSysMore.map((item: any) => {
                  const labelRow = {
                    columns: [
                      {
                        canvas: [
                          {
                            type: 'ellipse',
                            x: 6, y: 6, r1: 6, r2: 6,
                            color: item.checked ? '#005284' : 'lightgray'
                          }
                        ],
                        width: 'auto'
                      },
                      {
                        text: item.label,
                        margin: [10, 0, 0, 0],
                        style: 'subheader'
                      }
                    ],
                    margin: [0, 5, 0, 5]
                  };

                  const fileList =
                    item.checked && item.attachment
                      ? (item.uploads && item.uploads.length > 0
                        ? item.uploads.map((upload: any, idx: number) => ({
                          text: upload.url.split('/').pop(),
                          link: `${process.env.IMAGE_URL}/docs/${upload.url}`,
                          color: '#1a73e8',
                          decoration: 'underline',
                          margin: [20, 0, 0, 0]
                        }))
                        : [{
                          text: 'No Documents Upload',
                          margin: [20, 0, 0, 0],
                          italics: true
                        }])
                      : [];

                  return {
                    stack: [labelRow, ...fileList],
                    margin: [0, 5, 0, 10]
                  };
                })
              ]
            }

          ]
          : [
            {
              stack: [
                { text: 'Fire Isolation Location(s)', style: 'header' },
                { text: 'No', style: 'header' },
              ]
            }
          ]),

        // Security Isolation
        ...(dcop?.isDcSecurity === 'Yes'
          ? [
            {
              stack: [
                { text: 'Security Isolation Location(s)', style: 'header' },
                ...securitySystems.map((item: any) => item.loc5.id !== undefined ? ({
                  table: {
                    widths: ['*'],
                    body: [
                      [
                        {
                          stack: [
                            {
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      text: `${item.loc5.loc5Title} - ${item.loc5.name}`,
                                      style: 'subheader',
                                      fillColor: '#005284',
                                      color: '#FFFFFF',
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders'
                            },
                            {
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      text: `${item.loc6?.loc6Title || ''} - ${item.loc6?.name || ''}`,
                                      style: 'subheader',
                                      fillColor: '#E1EBEE',
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders'
                            },
                            ...item.systems.map((item1: any) => ({
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      columns: [
                                        {
                                          text: `Is the ${item1.label} Isolated?`,
                                          style: 'question',
                                          width: '*',
                                        },
                                        {
                                          text: item1.isolated === 1 ? 'Yes' : item1.isolated === 0 ? 'No' : 'Not Applicable',
                                          style: item1.isolated === 1 ? 'green' : item1.isolated === 0 ? 'red' : 'orange',
                                          width: '*',
                                        },
                                        ...(item1.remarks ? [{ text: `Remarks: ${item1.remarks}`, width: '*' }] : [])
                                      ].filter(Boolean),
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders',
                              margin: [0, 0, 0, 10],
                              keepTogether: true
                            }))
                          ],
                          keepTogether: true,
                          margin: [10, 10, 10, 10]
                        }
                      ]
                    ],
                    keepTogether: true
                  },
                  layout: {
                    hLineWidth: function (i: number, node: any) {
                      return (i === 0 || i === node.table.body.length) ? 1 : 0;
                    },
                    vLineWidth: function (i: number, node: any) {
                      return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                    },
                    hLineColor: function (i: number, node: any) {
                      return (i === 0 || i === node.table.body.length) ? '#E0E0E0' : 'white';
                    },
                    vLineColor: function (i: number, node: any) {
                      return (i === 0 || i === node.table.widths.length) ? '#E0E0E0' : 'white';
                    },
                  },
                  margin: [0, 10, 0, 10]
                }) : ({
                  stack: [
                    { text: 'N/A' }
                  ],
                  margin: [0, 10, 0, 10]
                }))
              ],
              canvas: [
                { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
              ],
              margin: [0, 10, 0, 10]
            }
          ]
          : [
            {
              stack: [
                { text: 'Security Isolation Location(s)', style: 'header' },
                { text: 'No', style: 'header' },
              ]
            }
          ]),

        ...(dcop?.isDcEnergy === 'Yes'
          ? [
            {
              stack: [
                { text: 'Energy Isolation Location(s)', style: 'header' },
                ...energySystems.map((item: any) => item.loc5.id !== undefined ? ({
                  table: {
                    widths: ['*'],
                    body: [
                      [
                        {
                          stack: [
                            {
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      text: `${item.loc5.loc5Title} - ${item.loc5.name}`,
                                      style: 'subheader',
                                      fillColor: '#005284',
                                      color: '#FFFFFF',
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders'
                            },
                            {
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      text: `${item.loc6?.loc6Title || ''} - ${item.loc6?.name || ''}`,
                                      style: 'subheader',
                                      fillColor: '#E1EBEE',
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders'
                            },
                            ...item.systems.map((item1: any) => ({
                              table: {
                                widths: ['*'],
                                body: [
                                  [
                                    {
                                      columns: [
                                        {
                                          text: `Is the ${item1.label} Isolated?`,
                                          style: 'question',
                                          width: '*',
                                        },
                                        {
                                          text: item1.isolated === 1 ? 'Yes' : item1.isolated === 0 ? 'No' : 'Not Applicable',
                                          style: item1.isolated === 1 ? 'green' : item1.isolated === 0 ? 'red' : 'orange',
                                          width: '*',
                                        },
                                        ...(item1.remarks ? [{ text: `Remarks: ${item1.remarks}`, width: '*' }] : [])
                                      ].filter(Boolean),
                                      margin: [5, 5, 5, 5]
                                    }
                                  ]
                                ]
                              },
                              layout: 'noBorders',
                              margin: [0, 0, 0, 10],
                              keepTogether: true
                            })),

                            ...(item.systemName?.length
                              ? item.systemName.map((tag: string) => ({
                                columns: [
                                  {
                                    text: 'Name/ID of the system(s) / device(s):',
                                    style: 'question',
                                    width: '*'
                                  },
                                  {
                                    text: tag,
                                    style: 'answer',
                                    width: '*'
                                  }
                                ],
                                margin: [5, 2, 5, 2]
                              }))
                              : [])
                          ],
                          keepTogether: true,
                          margin: [10, 10, 10, 10]
                        }
                      ]
                    ],
                    keepTogether: true
                  },
                  layout: {
                    hLineWidth: function (i: number, node: any) {
                      return (i === 0 || i === node.table.body.length) ? 1 : 0;
                    },
                    vLineWidth: function (i: number, node: any) {
                      return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                    },
                    hLineColor: function (i: number, node: any) {
                      return (i === 0 || i === node.table.body.length) ? '#E0E0E0' : 'white';
                    },
                    vLineColor: function (i: number, node: any) {
                      return (i === 0 || i === node.table.widths.length) ? '#E0E0E0' : 'white';
                    },
                  },
                  margin: [0, 10, 0, 10]
                }) : ({
                  stack: [
                    { text: 'N/A' }
                  ],
                  margin: [0, 10, 0, 10]
                }))
              ],
              canvas: [
                { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
              ],
              margin: [0, 10, 0, 10]
            }
          ]
          : [
            {
              stack: [
                { text: 'Energy Isolation Location(s)', style: 'header' },
                { text: 'No', style: 'header' },
              ]
            }
          ]),

        // High Risk (Non-CA)
        ...(dcop?.highRisk === 'Yes' && reportData.permitType !== 'CA'
          ? [
            {
              stack: [
                { text: 'High Risk activities involved in this work:' },
                ...permits
                  .filter((item: { checked: string }) => item.checked === 'Yes')
                  .map((item: { label: string }, index: number) => ({
                    text: `${index + 1}. ${item.label}`,
                    style: 'listItem',
                    bold: true
                  })),
                ...(permits.some((data: { checked: string }) => data.checked === 'Yes')
                  ? [{
                    stack: [
                      { text: 'Safety Checklist', style: 'safetyChecklistHeader' },
                      { text: '*Physical Verification of Compliance Required', style: 'safetyChecklistSubheader' },
                      ...permitChecklist.map((item: any, index: number) => {
                        const hasMatch = permits.some((data: { id: number, checked: string }) =>
                          item.applicable.includes(data.id - 1) && data.checked === 'Yes'
                        );
                        return hasMatch ? {
                          table: {
                            widths: ['*'],
                            body: [
                              [
                                {
                                  stack: [
                                    { text: `${index + 1}. ${item.label}`, style: 'checklistItem' },
                                    ...permits.map((data: any) => {
                                      if (item.applicable.includes(data.id - 1) && data.checked === 'Yes') {
                                        return {
                                          stack: [
                                            { text: `Confirmed for ${data.label} Activity?`, style: 'activityQuestion' },
                                            { text: getConfirmationLabel(data, item.id), style: 'activityAnswer' },
                                            ...(checklists[item.id][data.id]?.personnel
                                              ? [{ text: `Name of the Person who inspected: ${getPersonnel(data, item.id)}`, style: 'activityAnswer' }]
                                              : []),
                                            ...(checklists[item.id][data.id]?.remarks
                                              ? [{ text: `Remarks: ${getRemarks(data, item.id)}`, style: 'activityAnswer' }]
                                              : [])
                                          ].filter(Boolean),
                                          margin: [0, 0, 0, 10]
                                        };
                                      }
                                      return null;
                                    }).filter(Boolean)
                                  ],
                                  margin: [10, 10, 10, 10]
                                }
                              ]
                            ]
                          },
                          layout: {
                            hLineWidth: function (i: number, node: any) {
                              return (i === 0 || i === node.table.body.length) ? 1 : 0;
                            },
                            vLineWidth: function (i: number, node: any) {
                              return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                            },
                            hLineColor: function (i: number, node: any) {
                              return (i === 0 || i === node.table.body.length) ? '#E0E0E0' : 'white';
                            },
                            vLineColor: function (i: number, node: any) {
                              return (i === 0 || i === node.table.widths.length) ? '#E0E0E0' : 'white';
                            },
                          },
                          margin: [0, 0, 0, 10]
                        } : null;
                      }).filter(Boolean)
                    ],
                    margin: [0, 10, 0, 10],
                    keepTogether: true
                  }] : [])
              ].filter(Boolean),
              canvas: [
                { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
              ],
              margin: [0, 10, 0, 10]
            }
          ]
          : (reportData.permitType !== 'CA'
            ? [
              {
                stack: [
                  { text: 'High Risk activities involved in this work', style: 'header' },
                  { text: 'No', style: 'header' },
                ]
              }
            ]
            : [])
        ),

        // High Risk (CA)
        ...(reportData.permitType === 'CA'
          ? [
            {
              stack: [
                { text: 'High Risk activities involved in this work:' },
                ...permits
                  .filter((item: { checked: string }) => item.checked === 'Yes')
                  .map((item: { label: string }, index: number) => ({
                    text: `${index + 1}. ${item.label}`,
                    style: 'listItem',
                    bold: true
                  })),
                ...(permits.some((data: { checked: string }) => data.checked === 'Yes')
                  ? [{
                    stack: [
                      { text: 'Safety Checklist', style: 'safetyChecklistHeader' },
                      { text: '*Physical Verification of Compliance Required', style: 'safetyChecklistSubheader' },
                      ...permitChecklist.map((item: any, index: number) => {
                        const hasMatch = permits.some((data: { id: number, checked: string }) =>
                          item.applicable.includes(data.id - 1) && data.checked === 'Yes'
                        );
                        return hasMatch ? {
                          table: {
                            widths: ['*'],
                            body: [
                              [
                                {
                                  stack: [
                                    { text: `${index + 1}. ${item.label}`, style: 'checklistItem' },
                                    ...permits.map((data: any) => {
                                      if (item.applicable.includes(data.id - 1) && data.checked === 'Yes') {
                                        return {
                                          stack: [
                                            { text: `Confirmed for ${data.label} Activity?`, style: 'activityQuestion' },
                                            { text: getConfirmationLabel(data, item.id), style: 'activityAnswer' },
                                            // Removed personnel/remarks for CA scenario as per your original code conditions
                                          ],
                                          margin: [0, 0, 0, 10]
                                        };
                                      }
                                      return null;
                                    }).filter(Boolean)
                                  ],
                                  margin: [10, 10, 10, 10]
                                }
                              ]
                            ]
                          },
                          layout: {
                            hLineWidth: function (i: number, node: any) {
                              return (i === 0 || i === node.table.body.length) ? 1 : 0;
                            },
                            vLineWidth: function (i: number, node: any) {
                              return (i === 0 || i === node.table.widths.length) ? 1 : 0;
                            },
                            hLineColor: function (i: number, node: any) {
                              return (i === 0 || i === node.table.body.length) ? '#E0E0E0' : 'white';
                            },
                            vLineColor: function (i: number, node: any) {
                              return (i === 0 || i === node.table.widths.length) ? '#E0E0E0' : 'white';
                            },
                          },
                          margin: [0, 0, 0, 10]
                        } : null;
                      }).filter(Boolean)
                    ],
                    margin: [0, 10, 0, 10],
                    keepTogether: true
                  }] : [])
              ].filter(Boolean),
              canvas: [
                { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
              ],
              margin: [0, 10, 0, 10]
            }
          ] : []),

        // Applicant Section (Non-CA)
        ...(dcop && reportData.permitType !== 'CA'
          ? [{
            stack: [
              {
                stack: [
                  { text: 'Applicant', style: 'header' },
                  {
                    text: " I confirm that I am the supervisor in charge of the activity(ies) and am suitably competent to carry out the Person-In-Charge function. I have read and fully understand the SWMS/SWP and all the safety precautions to be taken under current legislations and STT GDC's Group Minimum Standards. I confirm all SWMS/SWP & Safety Checklist Conditions are complied with. To the best of my knowledge, this activity is safe to proceed.", // original text
                    style: "info"
                  },
                  ...(dcop.applicantSign ? [{
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${dcop.applicantSign}`),
                    width: 200,
                  }] : []),
                  { text: dcop.applicantName, style: 'info' },
                  { text: `Signed on: ${dcop.applicantSignedDate}`, style: 'info' },
                ],
                margin: [0, 10, 0, 10],
              },
              ...(dcop.approverSign ? [{
                stack: [
                  { text: 'Approver', style: 'header' },
                  {
                    text:
                      'I am satisfied that all safety processes and requirements are in place. Permission is hereby granted to the applicant of this permit to carry out the above-mentioned activity(ies) at the above-mentioned area(s).',
                    style: 'info',
                  },
                  {
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${dcop.approverSign}`),
                    width: 200,
                  },
                  { text: dcop.approverName, style: 'info' },
                  { text: `Signed on: ${dcop.approverSignedDate}`, style: 'info' },
                  {
                    stack: [
                      { text: 'Approver Comments', style: 'header' },
                      { text: dcop.approverComments || 'No comments', style: 'info' },
                    ],
                    margin: [0, 10, 0, 10],
                  },
                ],
                margin: [0, 10, 0, 10],
              }] : [])
            ].filter(Boolean),
            canvas: [{ type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }],
            margin: [0, 10, 0, 10],
          }] : []),

        // High Risk Sections (Non-CA), if dcop.highRisk === 'Yes'
        ...(dcop && dcop.highRisk === 'Yes'
          ? [{
            stack: [
              ...(reportData.high_risk && reportData.high_risk.assessorSign && (['Active', 'Approval', 'Rejected', 'Revoked', 'Closed', 'Pending DCSO Isolation'].some((status) =>
                reportData.status.includes(status)
              )) ? [{
                stack: [
                  { text: 'Assessor', style: 'header' },
                  {
                    text: "I confirm that I hold the appropriate qualification and competence to assess this permit. I have reviewed all aspects of the task(s)/activity(ies) and confirm that all SWMS/SWP & Safety Checklist conditions are complied with. To the best of my knowledge, this activity is safe to proceed.",
                    style: "info"
                  },
                  ...(reportData.high_risk?.assessorSign ? [{
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${reportData.high_risk?.assessorSign}`),
                    width: 200,
                  }] : []),
                  { text: reportData.high_risk?.assessorName, style: 'info' },
                  { text: `Signed on: ${reportData.high_risk?.assessorSignedDate}`, style: 'info' },
                  ...(['Active', 'Approval', 'Rejected', 'Pending DCSO Isolation'].some((status) =>
                    reportData.status.includes(status)
                  ) ? [{
                    stack: [
                      { text: 'Assessor Comments', style: 'header' },
                      { text: reportData.high_risk?.assessorComments || 'No comments', style: 'info' },
                    ],
                    margin: [0, 10, 0, 10],
                  }] : [])
                ],
                margin: [0, 10, 0, 10],
              }] : []),

              ...((['Active', 'DCSO', 'Rejected', 'Revoked', 'Closed', 'Closed & Pending', 'Pending DCSO Isolation'].some((status) =>
                reportData.status.includes(status)
              )) ? [{
                stack: [
                  { text: 'Approver', style: 'header' },
                  {
                    text:
                      'I am satisfied that all safety processes and requirements are in place. Permission is hereby granted to the applicant of this permit to carry out the above-mentioned activity(ies) at the above-mentioned area(s).',
                    style: 'info',
                  },
                  ...(reportData.high_risk?.approverSign ? [{
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${reportData.high_risk?.approverSign}`),
                    width: 200,
                  }] : []),
                  { text: reportData.high_risk?.approverName, style: 'info' },
                  { text: `Signed on: ${reportData.high_risk?.approverSignedDate}`, style: 'info' },
                  ...(['Active', 'Rejected', 'Closed & Pending', 'Pending DCSO Isolation'].some((status) =>
                    reportData.status.includes(status)
                  ) ? [{
                    stack: [
                      { text: 'Approver Comments', style: 'header' },
                      { text: reportData.high_risk?.approverComments || 'No comments', style: 'info' },
                    ],
                    margin: [0, 10, 0, 10],
                  }] : [])
                ],
                margin: [0, 10, 0, 10],
              }] : [])
            ].filter(Boolean),
            canvas: [{ type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }],
            margin: [0, 10, 0, 10],
          }] : []),

        // DCSO Representative
        ...(dcop && dcop.dcsoName &&
          (['Active', 'Rejected', 'Revoked', 'Closed', 'Closed & Pending'].some((status) =>
            reportData.status.includes(status)
          ))
          ? [{
            stack: [
              { text: 'DCSO Representative', style: 'header' },
              {
                text: "I acknowledge this permit application. I confirm that where applicable, work area hazards have been communicated,isolation has been completed and made safe in compliance with current legislations and STT GDC requirements. To the best of my knowledge, the work area is handed over to the relevant party in a safe condition.",
                style: "info"
              },
              ...(dcop.dcsoSign ? [{
                image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${dcop.dcsoSign}`),
                width: 200,
              }] : []),
              { text: dcop?.dcsoName, style: 'info' },
              { text: `Signed on: ${dcop.dcsoSignedDate}`, style: 'info' },
              ...(['Active', 'Rejected', 'Closed & Pending'].some((status) =>
                reportData.status.includes(status)
              ) ? [{
                stack: [
                  { text: 'DCSO Comments', style: 'header' },
                  { text: dcop?.dcsoComments || 'No comments', style: 'info' },
                ],
                margin: [0, 10, 0, 10],
              }] : [])
            ].filter(Boolean),
            canvas: [{ type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }],
            margin: [0, 10, 0, 10],
          }] : []),

        // CA Applicant/Assessor/Approver section if permitType === 'CA'
        ...(reportData.permitType === 'CA' ? [
          {
            stack: [
              {
                stack: [
                  { text: 'Applicant', style: 'header' },
                  {
                    text: "I confirm that I am the supervisor in charge of the activity(ies) and am suitably competent to carry out the Person-In-Charge function. I have read and fully understand the SWMS/SWP and all the safety precautions to be taken under current legislations and STT GDC's Group Minimum Standards. I confirm all SWMS/SWP & Safety Checklist Conditions are complied with To the best of my knowledge, this activity is safe to proceed.",
                    style: 'info',
                  },
                  ...(reportData.high_risk?.applicantSign ? [{
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${reportData.high_risk.applicantSign}`),
                    width: 200,
                  }] : []),
                  { text: reportData.high_risk.applicantName, style: 'info' },
                  { text: `Signed on: ${reportData.high_risk.applicantSignedDate}`, style: 'info' },
                ],
                margin: [0, 10, 0, 10],
              },

              ...(reportData.high_risk && reportData.high_risk.assessorSign && ['Active', 'Approval', 'Rejected', 'Revoked', 'Closed'].some(status => reportData.status.includes(status)) ? [{
                stack: [
                  { text: 'Assessor', style: 'header' },
                  {
                    text: "I confirm that I hold the appropriate qualification and competence to assess this permit. I have reviewed all aspects of the task(s)/activity(ies) and confirm that all SWMS/SWP & Safety Checklist conditions are complied with. To the best of my knowledge, this activity is safe to proceed.",
                    style: 'info',
                  },
                  ...(reportData.high_risk?.assessorSign ? [{
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${reportData.high_risk.assessorSign}`),
                    width: 200,
                  }] : []),
                  { text: reportData.high_risk?.assessorName, style: 'info' },
                  { text: `Signed on: ${reportData.high_risk?.assessorSignedDate}`, style: 'info' },
                  ...(['Active', 'Approval', 'Rejected'].some(status => reportData.status.includes(status)) ? [{
                    stack: [
                      { text: 'Assessor Comments', style: 'header' },
                      { text: reportData.high_risk?.assessorComments || 'No comments', style: 'info' },
                    ],
                    margin: [0, 10, 0, 10],
                  }] : [])
                ],
                margin: [0, 10, 0, 10],
              }] : []),

              ...(['Active', 'DCSO', 'Rejected', 'Revoked', 'Closed', 'Closed & Pending', 'Pending DCSO Isolation'].some(status => reportData.status.includes(status)) ? [{
                stack: [
                  { text: 'Approver', style: 'header' },
                  {
                    text: 'I am satisfied that all safety processes and requirements are in place. Permission is hereby granted to the applicant of this permit to carry out the above-mentioned activity(ies) at the above-mentioned area(s).',
                    style: 'info',
                  },
                  ...(reportData.high_risk?.approverSign ? [{
                    image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${reportData.high_risk.approverSign}`),
                    width: 200,
                  }] : []),
                  { text: reportData.high_risk?.approverName, style: 'info' },
                  { text: `Signed on: ${reportData.high_risk?.approverSignedDate}`, style: 'info' },
                  ...(['Active', 'Rejected', 'Closed & Pending', 'Pending DCSO Isolation'].some(status => reportData.status.includes(status)) ? [{
                    stack: [
                      { text: 'Approver Comments', style: 'header' },
                      { text: reportData.high_risk?.approverComments || 'No comments', style: 'info' },
                    ],
                    margin: [0, 10, 0, 10],
                  }] : [])
                ],
                margin: [0, 10, 0, 10],
              }] : []),
            ].filter(Boolean),
            canvas: [
              { type: 'line', x1: 0, y1: 0, x2: 520, y2: 0, lineWidth: 1, lineColor: '#E0E0E0' }
            ],
            margin: [0, 10, 0, 10],
          }
        ] : []),

        ...(closureSection ? [closureSection] : []),

        ...(reportData.closure && reportData.closure.applicantSign ? [{
          stack: [
            { text: 'Closure', style: 'header' },
            { text: 'I declare that I have closed the permit...', style: 'info' },
            {
              image: await convertToDataUrl(`${process.env.IMAGE_URL}/docs/${reportData.closure.applicantSign}`),
              width: 200,
            },
            { text: reportData.applicant.firstName, style: 'info' },
            { text: `Signed on: ${reportData.closure?.applicantSignedDate}`, style: 'info' },
          ],
          margin: [0, 10, 0, 10],
        }] : []),

        ...dcsoActions,
      ].filter(Boolean),
      styles: {
        header: {
          fontSize: 15,
          bold: true,
          margin: [0, 10, 0, 10]
        },
        subheader: {
          fontSize: 14,
          bold: true,
          margin: [0, 5, 0, 5]
        },
        question: {
          fontSize: 12,
          bold: true
        },
        green: {
          color: 'green'
        },
        red: {
          color: 'red'
        },
        orange: {
          color: 'orange'
        },
        listItem: {
          margin: [0, 5, 0, 5]
        },
        safetyChecklistHeader: {
          fontSize: 16,
          bold: true,
          margin: [0, 10, 0, 5]
        },
        safetyChecklistSubheader: {
          fontSize: 12,
          italics: true,
          margin: [0, 0, 0, 10]
        },
        checklistItem: {
          margin: [0, 5, 0, 5]
        },
        activityQuestion: {
          margin: [0, 2, 0, 2],
          bold: true
        },
        activityAnswer: {
          margin: [0, 2, 0, 2]
        },
        info: {
          fontSize: 12,
          margin: [0, 5, 0, 5]
        }
      }
    };

    const pdfDoc = printer.createPdfKitDocument(docDefinition);

    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];

      pdfDoc.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      pdfDoc.on('end', () => {
        const result = Buffer.concat(chunks);
        resolve(result);
      });

      pdfDoc.on('error', (err: Error) => {
        console.error('PDF generation error:', err);
        reject(err);
      });

      pdfDoc.end();
    });
  }

  async generateInspectionReportPdf(reportData: any): Promise<Buffer> {
    const fonts = {
      Roboto: {
        normal: path.resolve(__dirname, 'fonts/Roboto-Regular.ttf'),
        bold: path.resolve(__dirname, 'fonts/Roboto-Medium.ttf'),
        italics: path.resolve(__dirname, 'fonts/Roboto-Italic.ttf'),
        bolditalics: path.resolve(__dirname, 'fonts/Roboto-MediumItalic.ttf'),
      },
    };

    const printer = new PdfPrinter(fonts);

    function getStatusColor(status: string): string {
      if (status.includes("all actions closed")) return 'green';
      if (status.includes("Open Actions")) return 'orange';
      if (status.includes("Completed")) return 'green';
      return 'gray';
    }

    function getStatusFromActionRatio(data: any): string {
      const actions = groupByDescription(data.inspectionData.totalActions || []);
      const completed = actions.filter((a: any) => a.lastActionType === 'approve' && a.lastStatus === 'submitted');
      if (actions.length === 0) return 'Completed';
      if (actions.length === completed.length) return 'Completed with all actions closed';
      return 'Completed with Open Actions';
    }

    function groupByDescription(data: any[]) {
      const grouped: any = {};
      data.forEach(item => {
        const key = item.description;
        if (!grouped[key]) {
          grouped[key] = { data: [], lastActionType: '', lastStatus: '' };
        }
        grouped[key].data.push(item);
        grouped[key].lastActionType = item.actionType;
        grouped[key].lastStatus = item.status;
      });
      return Object.values(grouped);
    }

    const checklistSection = (reportData.checklistReport || []).map((section: any, idx: number) => {
      if (!section.checked) {
        return {
          text: `No inspection carried out for section: ${section.label || `Section ${idx + 1}`}`,
          italics: true,
          color: 'gray',
          margin: [0, 5, 0, 10],
        };
      }

      return {
        stack: [
          { text: section.label || `Section ${idx + 1}`, style: 'subheader' },
          ...(section.questions || []).map((q: any) => {
            const selected = (q.options || []).filter((opt: any) => opt.checked === 1).map((opt: any) => opt.label).join(', ') || 'N/A';
            return {
              stack: [
                { text: `Q: ${q.label}`, bold: true },
                { text: `Answer: ${selected}` },
                ...(q.remarks ? [{ text: `Remarks: ${q.remarks}`, italics: true }] : [])
              ],
              margin: [0, 0, 0, 10]
            };
          })
        ],
        margin: [0, 10, 0, 20]
      };
    });

    const actions = groupByDescription(reportData.inspectionData?.totalActions || []);

    const docDefinition: any = {
      pageMargins: [40, 60, 40, 40],
      content: [
        // Header
        {
          stack: [
            { text: 'Inspection Report', style: 'header' },
            { text: reportData.checklist?.name || 'Safety Inspection', style: 'subheader' },
            {
              text: getStatusFromActionRatio(reportData),
              color: getStatusColor(getStatusFromActionRatio(reportData)),
              bold: true,
              margin: [0, 5, 0, 10]
            }
          ]
        },

        // Summary Table
        {
          table: {
            widths: ['*', '*'],
            body: [
              ['Reference ID', reportData.maskId || ''],
              ['Scheduler', reportData.assignedBy?.firstName || ''],
              ['Inspector', reportData.assignedTo?.firstName || ''],
              ['Assigned Date', moment(reportData.created).format('Do MMM YYYY')],
              ['Due Date', moment(reportData.dateTime).format('Do MMM YYYY')],
              ['Location', [reportData.locationOne?.name, reportData.locationTwo?.name, reportData.locationThree?.name, reportData.locationFour?.name].filter(Boolean).join(' → ')]
            ]
          },
          layout: 'lightHorizontalLines',
          margin: [0, 0, 0, 20]
        },

        // Checklist
        { text: 'Inspection Checklists', style: 'subheader' },
        ...checklistSection,

        // Actions
        ...(actions.length > 0
          ? [
            { text: `Action Items (${actions.length})`, style: 'subheader', margin: [0, 10, 0, 10] },
            {
              table: {
                widths: ['auto', '*', 'auto', 'auto'],
                body: [
                  ['#', 'Description', 'Status', 'Action Type'],
                  ...actions.map((item: any, index: number) => [
                    `${index + 1}`,
                    item.data?.[0]?.description || '',
                    item.lastStatus || '',
                    item.lastActionType || ''
                  ])
                ]
              },
              layout: 'lightHorizontalLines',
              margin: [0, 0, 0, 20]
            }
          ]
          : []
        ),

        // Footer
        {
          columns: [
            { text: `Generated on: ${moment().format('Do MMMM YYYY, h:mm A')}`, fontSize: 10, color: 'gray' },
            { text: 'Safety & Inspection Management System', alignment: 'right', fontSize: 10, color: 'gray' }
          ],
          margin: [0, 30, 0, 0]
        }
      ],
      styles: {
        header: { fontSize: 18, bold: true, margin: [0, 10, 0, 10] },
        subheader: { fontSize: 14, bold: true, margin: [0, 10, 0, 5] },
      }
    };

    const pdfDoc = printer.createPdfKitDocument(docDefinition);

    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];

      pdfDoc.on('data', chunk => chunks.push(chunk));
      pdfDoc.on('end', () => resolve(Buffer.concat(chunks)));
      pdfDoc.on('error', reject);

      pdfDoc.end();
    });
  }
}
